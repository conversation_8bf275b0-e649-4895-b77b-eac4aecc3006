{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build", "start": "next start", "lint": "next lint", "preview": "next start", "test": "jest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:full": "playwright test tests/e2e/full-suite.spec.ts", "test:e2e:homepage": "playwright test tests/e2e/homepage.spec.ts", "test:e2e:navigation": "playwright test tests/e2e/navigation.spec.ts", "test:e2e:responsive": "playwright test tests/e2e/responsive.spec.ts", "test:e2e:accessibility": "playwright test tests/e2e/accessibility.spec.ts", "test:e2e:performance": "playwright test tests/e2e/performance.spec.ts", "test:e2e:environment": "playwright test tests/e2e/environment.spec.ts", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:e2e:desktop": "playwright test --project=chromium --project=firefox --project=webkit", "test:install": "playwright install", "test:install:deps": "playwright install-deps", "env:validate": "node scripts/validate-env.cjs", "env:validate:production": "node scripts/validate-env.cjs .env.production", "security:check": "node scripts/validate-env.cjs && echo '✅ Security validation passed'", "security:audit": "npm audit && node scripts/validate-env.cjs", "monitoring:validate": "node scripts/validate-monitoring.cjs", "monitoring:validate:endpoints": "node scripts/validate-monitoring.cjs --test-endpoints", "db:check": "node -e \"require('./src/lib/supabase.ts').checkDatabaseConnection().then(r => console.log('DB OK:', r))\""}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@sentry/nextjs": "^9.42.0", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.526.0", "next": "^15.4.4", "next-pwa": "^5.6.0", "posthog-js": "^1.258.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-feather": "^2.0.10", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "dotenv": "^17.2.1", "eslint": "^9.9.1", "eslint-config-next": "^15.4.4", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-pwa": "^1.0.0"}}