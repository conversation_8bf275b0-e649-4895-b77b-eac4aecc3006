/**
 * Comprehensive monitoring configuration for production deployment
 * Ensures Sentry, PostHog, and performance monitoring are properly configured
 */

import * as Sentry from "@sentry/nextjs";
import { getEnvironmentConfig, isProduction, isDevelopment } from './env';
import { 
  initPostHog, 
  isPostHogReady, 
  checkPostHogHealth,
  getPostHogHealthStatus 
} from './posthog';
import { getSentryConfig, reportError, reportBusinessError } from './sentry';

export interface MonitoringConfig {
  sentry: {
    enabled: boolean;
    dsn: string;
    environment: string;
    tracesSampleRate: number;
    replaysSessionSampleRate: number;
    replaysOnErrorSampleRate: number;
  };
  posthog: {
    enabled: boolean;
    apiKey: string;
    apiHost: string;
    initialized: boolean;
    healthy: boolean;
  };
  performance: {
    webVitalsEnabled: boolean;
    performanceObserverEnabled: boolean;
    resourceTimingEnabled: boolean;
  };
}

export interface MonitoringHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    sentry: {
      status: 'up' | 'down' | 'degraded';
      configured: boolean;
      error?: string;
    };
    posthog: {
      status: 'up' | 'down' | 'degraded';
      initialized: boolean;
      healthy: boolean;
      error?: string;
    };
    performance: {
      status: 'up' | 'down' | 'degraded';
      webVitals: boolean;
      observer: boolean;
      error?: string;
    };
  };
  lastChecked: string;
}

// Global monitoring state
let monitoringInitialized = false;
let monitoringConfig: MonitoringConfig | null = null;
let lastHealthCheck: MonitoringHealthStatus | null = null;

/**
 * Initialize comprehensive monitoring for production
 */
export async function initializeMonitoring(): Promise<boolean> {
  if (monitoringInitialized) {
    console.log('📊 Monitoring already initialized');
    return true;
  }

  try {
    console.log('📊 Initializing comprehensive monitoring...');
    
    // Get environment configuration
    const envConfig = getEnvironmentConfig();
    
    // Initialize Sentry (should already be done by Next.js config files)
    const sentryConfig = getSentryConfig();
    const sentryEnabled = sentryConfig !== null;
    
    if (sentryEnabled) {
      console.log('✅ Sentry monitoring configured');
      
      // Set up global error handlers
      if (typeof window !== 'undefined') {
        // Browser-specific error handling
        window.addEventListener('unhandledrejection', (event) => {
          reportError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
            page: { url: window.location.href, section: 'global' },
            browser: { 
              name: navigator.userAgent, 
              version: navigator.appVersion,
              userAgent: navigator.userAgent 
            },
            technical: { 
              errorType: 'unhandledrejection',
              reason: event.reason 
            }
          });
        });

        window.addEventListener('error', (event) => {
          reportError(event.error || new Error(event.message), {
            page: { url: window.location.href, section: 'global' },
            browser: { 
              name: navigator.userAgent, 
              version: navigator.appVersion,
              userAgent: navigator.userAgent 
            },
            technical: { 
              errorType: 'javascript',
              filename: event.filename,
              lineno: event.lineno,
              colno: event.colno
            }
          });
        });
      }
    } else {
      console.warn('⚠️ Sentry monitoring not configured');
    }

    // Initialize PostHog
    let posthogInitialized = false;
    let posthogHealthy = false;
    
    try {
      await initPostHog();
      posthogInitialized = isPostHogReady();
      posthogHealthy = await checkPostHogHealth();
      
      if (posthogInitialized) {
        console.log('✅ PostHog analytics configured');
      } else {
        console.warn('⚠️ PostHog analytics initialization failed');
      }
    } catch (error) {
      console.error('❌ PostHog initialization error:', error);
      if (sentryEnabled) {
        reportError(error as Error, {
          page: { url: 'server', section: 'monitoring-init' },
          browser: { name: 'server', version: 'unknown', userAgent: 'server' },
          business: { operation: 'posthog_initialization' }
        });
      }
    }

    // Initialize performance monitoring
    let performanceEnabled = false;
    try {
      if (typeof window !== 'undefined') {
        await initializePerformanceMonitoring();
        performanceEnabled = true;
        console.log('✅ Performance monitoring configured');
      }
    } catch (error) {
      console.error('❌ Performance monitoring initialization error:', error);
      if (sentryEnabled) {
        reportError(error as Error, {
          page: { url: 'client', section: 'monitoring-init' },
          browser: { name: 'client', version: 'unknown', userAgent: 'client' },
          business: { operation: 'performance_monitoring_initialization' }
        });
      }
    }

    // Store monitoring configuration
    monitoringConfig = {
      sentry: {
        enabled: sentryEnabled,
        dsn: sentryConfig?.dsn.substring(0, 20) + '...' || 'not configured',
        environment: sentryConfig?.environment || 'unknown',
        tracesSampleRate: sentryConfig?.tracesSampleRate || 0,
        replaysSessionSampleRate: isProduction() ? 0.1 : 0.1,
        replaysOnErrorSampleRate: isProduction() ? 1.0 : 0.5,
      },
      posthog: {
        enabled: posthogInitialized,
        apiKey: envConfig.posthog.apiKey.substring(0, 10) + '...',
        apiHost: envConfig.posthog.apiHost,
        initialized: posthogInitialized,
        healthy: posthogHealthy,
      },
      performance: {
        webVitalsEnabled: performanceEnabled,
        performanceObserverEnabled: performanceEnabled && 'PerformanceObserver' in window,
        resourceTimingEnabled: performanceEnabled && 'performance' in window,
      },
    };

    monitoringInitialized = true;
    console.log('✅ Comprehensive monitoring initialized successfully');
    
    // Perform initial health check
    await checkMonitoringHealth();
    
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize monitoring:', error);
    
    // Try to report the error if Sentry is available
    try {
      reportError(error as Error, {
        page: { url: 'server', section: 'monitoring-init' },
        browser: { name: 'server', version: 'unknown', userAgent: 'server' },
        business: { operation: 'monitoring_initialization' }
      });
    } catch (sentryError) {
      console.error('❌ Failed to report monitoring initialization error to Sentry:', sentryError);
    }
    
    return false;
  }
}

/**
 * Initialize performance monitoring with Web Vitals
 */
async function initializePerformanceMonitoring(): Promise<void> {
  if (typeof window === 'undefined') return;

  try {
    // Import web-vitals dynamically
    const { onCLS, onFID, onFCP, onLCP, onTTFB, onINP } = await import('web-vitals');
    
    // Report Web Vitals to Sentry
    const reportWebVital = (name: string, value: number, id: string) => {
      // Report to Sentry
      Sentry.setMeasurement(name, value, name === 'CLS' ? 'ratio' : 'millisecond');
      
      // Add breadcrumb for debugging
      Sentry.addBreadcrumb({
        message: `Web Vital: ${name}`,
        category: 'performance',
        level: 'info',
        data: { name, value, id },
      });

      if (isDevelopment()) {
        console.log(`📊 Web Vital - ${name}:`, value, id);
      }
    };

    // Set up Web Vitals monitoring
    onCLS((metric) => reportWebVital('CLS', metric.value, metric.id));
    onFID((metric) => reportWebVital('FID', metric.value, metric.id));
    onFCP((metric) => reportWebVital('FCP', metric.value, metric.id));
    onLCP((metric) => reportWebVital('LCP', metric.value, metric.id));
    onTTFB((metric) => reportWebVital('TTFB', metric.value, metric.id));
    onINP((metric) => reportWebVital('INP', metric.value, metric.id));

    // Set up Performance Observer for additional metrics
    if ('PerformanceObserver' in window) {
      // Monitor long tasks
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              Sentry.addBreadcrumb({
                message: 'Long Task Detected',
                category: 'performance',
                level: 'warning',
                data: {
                  duration: entry.duration,
                  startTime: entry.startTime,
                  name: entry.name,
                },
              });
            }
          }
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        console.warn('Long task observer not supported:', error);
      }

      // Monitor navigation timing
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const navEntry = entry as PerformanceNavigationTiming;
            
            // Report key navigation metrics
            const metrics = {
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
              domInteractive: navEntry.domInteractive - navEntry.navigationStart,
              firstByte: navEntry.responseStart - navEntry.requestStart,
            };

            Object.entries(metrics).forEach(([name, value]) => {
              if (value > 0) {
                Sentry.setMeasurement(`navigation.${name}`, value, 'millisecond');
              }
            });
          }
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
      } catch (error) {
        console.warn('Navigation observer not supported:', error);
      }
    }

  } catch (error) {
    console.error('Failed to initialize performance monitoring:', error);
    throw error;
  }
}

/**
 * Check the health of all monitoring services
 */
export async function checkMonitoringHealth(): Promise<MonitoringHealthStatus> {
  const timestamp = new Date().toISOString();
  
  try {
    // Check Sentry health
    const sentryConfig = getSentryConfig();
    const sentryStatus = sentryConfig ? 'up' : 'down';
    
    // Check PostHog health
    const posthogHealthStatus = getPostHogHealthStatus();
    const posthogHealthy = await checkPostHogHealth();
    
    let posthogStatus: 'up' | 'down' | 'degraded' = 'down';
    if (posthogHealthStatus.initialized && posthogHealthy) {
      posthogStatus = 'up';
    } else if (posthogHealthStatus.initialized && !posthogHealthy) {
      posthogStatus = 'degraded';
    }

    // Check performance monitoring
    const performanceStatus = typeof window !== 'undefined' && 'performance' in window ? 'up' : 'down';

    // Determine overall health
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (sentryStatus === 'down' && posthogStatus === 'down') {
      overallStatus = 'unhealthy';
    } else if (sentryStatus === 'down' || posthogStatus === 'down' || performanceStatus === 'down') {
      overallStatus = 'degraded';
    } else if (posthogStatus === 'degraded') {
      overallStatus = 'degraded';
    }

    const healthStatus: MonitoringHealthStatus = {
      overall: overallStatus,
      services: {
        sentry: {
          status: sentryStatus,
          configured: sentryConfig !== null,
          error: sentryStatus === 'down' ? 'Sentry not configured' : undefined,
        },
        posthog: {
          status: posthogStatus,
          initialized: posthogHealthStatus.initialized,
          healthy: posthogHealthy,
          error: posthogStatus === 'down' ? 'PostHog not initialized' : 
                 posthogStatus === 'degraded' ? posthogHealthStatus.lastError : undefined,
        },
        performance: {
          status: performanceStatus,
          webVitals: typeof window !== 'undefined',
          observer: typeof window !== 'undefined' && 'PerformanceObserver' in window,
          error: performanceStatus === 'down' ? 'Performance monitoring not available' : undefined,
        },
      },
      lastChecked: timestamp,
    };

    lastHealthCheck = healthStatus;
    return healthStatus;
    
  } catch (error) {
    console.error('Failed to check monitoring health:', error);
    
    const fallbackStatus: MonitoringHealthStatus = {
      overall: 'unhealthy',
      services: {
        sentry: { status: 'down', configured: false, error: 'Health check failed' },
        posthog: { status: 'down', initialized: false, healthy: false, error: 'Health check failed' },
        performance: { status: 'down', webVitals: false, observer: false, error: 'Health check failed' },
      },
      lastChecked: timestamp,
    };

    lastHealthCheck = fallbackStatus;
    return fallbackStatus;
  }
}

/**
 * Get current monitoring configuration
 */
export function getMonitoringConfig(): MonitoringConfig | null {
  return monitoringConfig;
}

/**
 * Get last monitoring health check result
 */
export function getLastMonitoringHealthCheck(): MonitoringHealthStatus | null {
  return lastHealthCheck;
}

/**
 * Check if monitoring is properly initialized
 */
export function isMonitoringInitialized(): boolean {
  return monitoringInitialized;
}

/**
 * Reset monitoring state (for testing or recovery)
 */
export function resetMonitoring(): void {
  monitoringInitialized = false;
  monitoringConfig = null;
  lastHealthCheck = null;
}

/**
 * Report monitoring service degradation
 */
export function reportMonitoringIssue(
  service: 'sentry' | 'posthog' | 'performance',
  issue: string,
  severity: 'low' | 'medium' | 'high' = 'medium'
): void {
  console.error(`📊 Monitoring issue in ${service}:`, issue);
  
  // Try to report to Sentry if it's not the failing service
  if (service !== 'sentry') {
    try {
      reportBusinessError({
        code: 'MONITORING_SERVICE_DEGRADED',
        message: `${service} monitoring service issue: ${issue}`,
        severity,
        category: 'external',
        context: { service, issue }
      }, {
        page: { url: 'monitoring', section: 'health-check' },
        browser: { name: 'system', version: 'unknown', userAgent: 'system' },
        business: { operation: 'monitoring_health_check', entityType: 'monitoring' }
      });
    } catch (error) {
      console.error('Failed to report monitoring issue to Sentry:', error);
    }
  }
}

/**
 * Validate monitoring configuration for production deployment
 */
export function validateMonitoringForProduction(): {
  valid: boolean;
  issues: string[];
  warnings: string[];
} {
  const issues: string[] = [];
  const warnings: string[] = [];

  try {
    const envConfig = getEnvironmentConfig();
    
    // Check Sentry configuration
    const sentryConfig = getSentryConfig();
    if (!sentryConfig) {
      issues.push('Sentry DSN not configured');
    } else {
      if (sentryConfig.environment === 'development' && isProduction()) {
        warnings.push('Sentry environment is set to development in production');
      }
      if (sentryConfig.tracesSampleRate > 0.2 && isProduction()) {
        warnings.push('Sentry traces sample rate is high for production (>20%)');
      }
    }

    // Check PostHog configuration
    if (!envConfig.posthog.apiKey || envConfig.posthog.apiKey === 'your_posthog_api_key') {
      issues.push('PostHog API key not configured');
    }
    
    if (!envConfig.posthog.apiHost) {
      warnings.push('PostHog API host not configured, using default');
    }

    // Check environment variables
    if (!process.env.NEXT_PUBLIC_SENTRY_DSN) {
      issues.push('NEXT_PUBLIC_SENTRY_DSN environment variable not set');
    }
    
    if (!process.env.NEXT_PUBLIC_POSTHOG_API_KEY) {
      issues.push('NEXT_PUBLIC_POSTHOG_API_KEY environment variable not set');
    }

    return {
      valid: issues.length === 0,
      issues,
      warnings,
    };
    
  } catch (error) {
    return {
      valid: false,
      issues: [`Failed to validate monitoring configuration: ${(error as Error).message}`],
      warnings: [],
    };
  }
}

// Export types for external use
export type { MonitoringConfig, MonitoringHealthStatus };