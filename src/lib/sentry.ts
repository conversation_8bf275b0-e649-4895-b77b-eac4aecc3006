import * as Sentry from "@sentry/nextjs";
import { getEnvironmentConfig, isProduction } from './env';

export interface SentryConfig {
  dsn: string;
  environment: "development" | "staging" | "production";
  tracesSampleRate: number;
}

export interface ErrorBoundaryProps {
  fallback: React.ComponentType<{ error: Error }>;
  children: React.ReactNode;
}

// Business logic error types
export interface BusinessError {
  code: string;
  message: string;
  context?: Record<string, any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  category?: 'validation' | 'network' | 'database' | 'auth' | 'business' | 'external';
}

// Performance monitoring types
export interface PerformanceMetrics {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  tags?: Record<string, string>;
}

// User session context
export interface UserSessionContext {
  userId?: string;
  email?: string;
  role?: string;
  sessionId: string;
  userAgent: string;
  timestamp: number;
  page: string;
  referrer?: string;
}

// Error context for business logic
export interface ErrorContext {
  user?: {
    id?: string;
    email?: string;
    role?: string;
  };
  page: {
    url: string;
    section: string;
    component?: string;
  };
  browser: {
    name: string;
    version: string;
    userAgent: string;
  };
  business?: {
    operation?: string;
    entityId?: string;
    entityType?: string;
    workflow?: string;
  };
  technical?: {
    apiEndpoint?: string;
    requestId?: string;
    responseTime?: number;
    statusCode?: number;
  };
}

// Environment-based Sentry configuration
export const getSentryConfig = (): SentryConfig | null => {
  try {
    const config = getEnvironmentConfig();
    
    return {
      dsn: config.sentry.dsn,
      environment: config.sentry.environment,
      tracesSampleRate: isProduction() ? 0.1 : 1.0,
    };
  } catch (error) {
    console.warn("Sentry configuration not available:", error);
    return null;
  }
};

// Note: Sentry initialization is handled by Next.js configuration files
// (sentry.client.config.ts, sentry.server.config.ts, sentry.edge.config.ts)
// This file provides utility functions for enhanced error reporting and monitoring

// Enhanced error reporting utility with business context
export const reportError = (error: Error, context?: ErrorContext): void => {
  Sentry.withScope((scope) => {
    if (context) {
      // Set user context
      if (context.user) {
        scope.setUser(context.user);
      }
      
      // Set page context
      if (context.page) {
        scope.setTag('page.url', context.page.url);
        scope.setTag('page.section', context.page.section);
        if (context.page.component) {
          scope.setTag('page.component', context.page.component);
        }
        scope.setContext('page', context.page);
      }
      
      // Set browser context
      if (context.browser) {
        scope.setTag('browser.name', context.browser.name);
        scope.setTag('browser.version', context.browser.version);
        scope.setContext('browser', context.browser);
      }
      
      // Set business context
      if (context.business) {
        scope.setTag('business.operation', context.business.operation || 'unknown');
        if (context.business.entityType) {
          scope.setTag('business.entityType', context.business.entityType);
        }
        scope.setContext('business', context.business);
      }
      
      // Set technical context
      if (context.technical) {
        if (context.technical.apiEndpoint) {
          scope.setTag('api.endpoint', context.technical.apiEndpoint);
        }
        if (context.technical.statusCode) {
          scope.setTag('api.statusCode', context.technical.statusCode.toString());
        }
        scope.setContext('technical', context.technical);
      }
    }
    
    Sentry.captureException(error);
  });
};

// Report business logic errors with structured context
export const reportBusinessError = (businessError: BusinessError, context?: ErrorContext): void => {
  const error = new Error(businessError.message);
  error.name = `BusinessError:${businessError.code}`;
  
  Sentry.withScope((scope) => {
    // Set error severity and category
    scope.setLevel(businessError.severity === 'critical' ? 'fatal' : 
                   businessError.severity === 'high' ? 'error' :
                   businessError.severity === 'medium' ? 'warning' : 'info');
    
    scope.setTag('error.type', 'business');
    scope.setTag('error.code', businessError.code);
    scope.setTag('error.category', businessError.category || 'business');
    
    // Set business error context
    scope.setContext('businessError', {
      code: businessError.code,
      category: businessError.category,
      severity: businessError.severity,
      context: businessError.context,
    });
    
    // Apply additional context if provided
    if (context) {
      if (context.user) scope.setUser(context.user);
      if (context.page) scope.setContext('page', context.page);
      if (context.browser) scope.setContext('browser', context.browser);
      if (context.business) scope.setContext('businessContext', context.business);
      if (context.technical) scope.setContext('technical', context.technical);
    }
    
    Sentry.captureException(error);
  });
};

// Performance monitoring utility with Core Web Vitals
export const startTransaction = (name: string, operation: string) => {
  return Sentry.startSpan({ 
    name, 
    op: operation
  }, (span) => {
    // Set initial attributes
    span?.setAttributes({ 'transaction.type': operation });
    return span;
  });
};

// Report performance metrics
export const reportPerformanceMetric = (metric: PerformanceMetrics): void => {
  Sentry.withScope((scope) => {
    scope.setTag('metric.name', metric.name);
    scope.setTag('metric.unit', metric.unit);
    
    if (metric.tags) {
      Object.entries(metric.tags).forEach(([key, value]) => {
        scope.setTag(`metric.${key}`, value);
      });
    }
    
    // Create a custom measurement
    Sentry.setMeasurement(metric.name, metric.value, metric.unit);
    
    // Also send as a custom event for detailed tracking
    Sentry.addBreadcrumb({
      message: `Performance metric: ${metric.name}`,
      category: 'performance',
      level: 'info',
      data: {
        name: metric.name,
        value: metric.value,
        unit: metric.unit,
        ...metric.tags,
      },
    });
  });
};

// Core Web Vitals monitoring
export const reportWebVital = (name: string, value: number, id: string): void => {
  reportPerformanceMetric({
    name: `web-vital.${name}`,
    value,
    unit: name === 'CLS' ? 'count' : 'ms',
    tags: {
      id,
      type: 'web-vital',
    },
  });
};

// Enhanced user session tracking
export const setUserSessionContext = (sessionContext: UserSessionContext): void => {
  Sentry.withScope((scope) => {
    // Set user information
    scope.setUser({
      id: sessionContext.userId,
      email: sessionContext.email,
    });
    
    // Set session tags
    scope.setTag('session.id', sessionContext.sessionId);
    scope.setTag('session.page', sessionContext.page);
    if (sessionContext.role) {
      scope.setTag('user.role', sessionContext.role);
    }
    
    // Set session context
    scope.setContext('session', {
      sessionId: sessionContext.sessionId,
      userAgent: sessionContext.userAgent,
      timestamp: sessionContext.timestamp,
      page: sessionContext.page,
      referrer: sessionContext.referrer,
    });
  });
};

// Enhanced breadcrumb system
export const addBreadcrumb = (
  message: string, 
  category: string, 
  level: Sentry.SeverityLevel = "info",
  data?: Record<string, any>
): void => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    timestamp: Date.now() / 1000,
    data,
  });
};

// Business operation breadcrumbs
export const addBusinessBreadcrumb = (
  operation: string,
  entityType: string,
  entityId?: string,
  result?: 'success' | 'error' | 'warning',
  data?: Record<string, any>
): void => {
  addBreadcrumb(
    `Business operation: ${operation}`,
    'business',
    result === 'error' ? 'error' : result === 'warning' ? 'warning' : 'info',
    {
      operation,
      entityType,
      entityId,
      result,
      ...data,
    }
  );
};

// API call breadcrumbs
export const addAPIBreadcrumb = (
  method: string,
  url: string,
  statusCode?: number,
  responseTime?: number,
  data?: Record<string, any>
): void => {
  addBreadcrumb(
    `API ${method} ${url}`,
    'api',
    statusCode && statusCode >= 400 ? 'error' : 'info',
    {
      method,
      url,
      statusCode,
      responseTime,
      ...data,
    }
  );
};

// User interaction breadcrumbs
export const addUserInteractionBreadcrumb = (
  action: string,
  element: string,
  page: string,
  data?: Record<string, any>
): void => {
  addBreadcrumb(
    `User ${action}: ${element}`,
    'user',
    'info',
    {
      action,
      element,
      page,
      ...data,
    }
  );
};

// Export Sentry for direct use
export { Sentry };