/**
 * PostHog Initialization Manager
 * Provides thread-safe, singleton-based initialization control for PostHog analytics
 */

import posthog from 'posthog-js';
import { getEnvironmentConfig, isDevelopment, isProduction } from './env';

export enum InitializationState {
  NOT_STARTED = 'not_started',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  FAILED = 'failed',
  DISABLED = 'disabled'
}

export interface PostHogHealthStatus {
  initialized: boolean;
  healthy: boolean;
  lastError?: string;
  initializationAttempts: number;
  state: InitializationState;
  lastAttempt?: number;
  initializationTime?: number;
}

export interface InitializationContext {
  state: InitializationState;
  startTime?: number;
  endTime?: number;
  error?: Error;
  retryCount: number;
  lastAttempt?: number;
}

/**
 * Thread-safe PostHog initialization manager with singleton pattern
 */
export class PostHogInitManager {
  private static instance: PostHogInitManager | null = null;
  private static initializationPromise: Promise<boolean> | null = null;
  
  private context: InitializationContext = {
    state: InitializationState.NOT_STARTED,
    retryCount: 0,
  };
  
  private healthStatus: PostHogHealthStatus = {
    initialized: false,
    healthy: false,
    initializationAttempts: 0,
    state: InitializationState.NOT_STARTED,
  };

  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly RETRY_DELAY_BASE = 1000; // 1 second
  private readonly INITIALIZATION_TIMEOUT = 10000; // 10 seconds

  /**
   * Get singleton instance
   */
  public static getInstance(): PostHogInitManager {
    if (!PostHogInitManager.instance) {
      PostHogInitManager.instance = new PostHogInitManager();
    }
    return PostHogInitManager.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    // Bind methods to preserve context
    this.initialize = this.initialize.bind(this);
    this.isInitialized = this.isInitialized.bind(this);
    this.isInitializing = this.isInitializing.bind(this);
    this.reset = this.reset.bind(this);
    this.getHealthStatus = this.getHealthStatus.bind(this);
  }

  /**
   * Initialize PostHog with thread-safe coordination
   */
  public async initialize(): Promise<boolean> {
    // Return existing promise if initialization is in progress
    if (PostHogInitManager.initializationPromise) {
      console.log('PostHog initialization already in progress, waiting...');
      return PostHogInitManager.initializationPromise;
    }

    // Return true if already initialized
    if (this.context.state === InitializationState.INITIALIZED) {
      console.log('PostHog already initialized');
      return true;
    }

    // Return false if disabled
    if (this.context.state === InitializationState.DISABLED) {
      console.log('PostHog is disabled');
      return false;
    }

    // Create new initialization promise
    PostHogInitManager.initializationPromise = this.performInitialization();
    
    try {
      const result = await PostHogInitManager.initializationPromise;
      return result;
    } finally {
      // Clear the promise after completion (success or failure)
      PostHogInitManager.initializationPromise = null;
    }
  }

  /**
   * Perform the actual initialization with retry logic
   */
  private async performInitialization(): Promise<boolean> {
    this.updateState(InitializationState.INITIALIZING);
    this.context.startTime = Date.now();
    this.healthStatus.initializationAttempts++;
    this.healthStatus.lastAttempt = Date.now();

    try {
      // Validate configuration first
      const config = this.getValidatedConfig();
      if (!config.isValid) {
        console.warn('PostHog configuration is invalid:', config.errors);
        
        if (isDevelopment()) {
          console.log('Development mode: continuing with disabled analytics');
          this.updateState(InitializationState.DISABLED);
          return false;
        } else {
          throw new Error(`PostHog configuration invalid: ${config.errors.join(', ')}`);
        }
      }

      // Initialize PostHog with timeout
      const initResult = await this.initializeWithTimeout(config.config);
      
      if (initResult) {
        this.updateState(InitializationState.INITIALIZED);
        this.context.endTime = Date.now();
        this.healthStatus.initialized = true;
        this.healthStatus.healthy = true;
        this.healthStatus.initializationTime = this.context.endTime - (this.context.startTime || 0);
        
        console.log(`✅ PostHog initialized successfully in ${this.healthStatus.initializationTime}ms`);
        return true;
      } else {
        throw new Error('PostHog initialization returned false');
      }

    } catch (error) {
      const errorObj = error as Error;
      console.error('PostHog initialization failed:', errorObj);
      
      this.context.error = errorObj;
      this.context.retryCount++;
      this.healthStatus.lastError = errorObj.message;

      // Retry logic
      if (this.context.retryCount < this.MAX_RETRY_ATTEMPTS) {
        console.log(`Retrying PostHog initialization (attempt ${this.context.retryCount + 1}/${this.MAX_RETRY_ATTEMPTS})`);
        
        // Wait before retry with exponential backoff
        const delay = this.RETRY_DELAY_BASE * Math.pow(2, this.context.retryCount - 1);
        await this.sleep(delay);
        
        // Recursive retry
        return this.performInitialization();
      } else {
        // Max retries reached
        this.updateState(InitializationState.FAILED);
        this.context.endTime = Date.now();
        this.healthStatus.healthy = false;
        
        console.error(`❌ PostHog initialization failed after ${this.MAX_RETRY_ATTEMPTS} attempts`);
        
        // In development, continue without analytics
        if (isDevelopment()) {
          console.log('Development mode: continuing without analytics');
          return false;
        }
        
        throw errorObj;
      }
    }
  }

  /**
   * Initialize PostHog with timeout protection
   */
  private async initializeWithTimeout(config: any): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`PostHog initialization timeout after ${this.INITIALIZATION_TIMEOUT}ms`));
      }, this.INITIALIZATION_TIMEOUT);

      try {
        // Check if PostHog is already initialized
        if (posthog.__loaded) {
          clearTimeout(timeoutId);
          console.log('✅ PostHog already loaded');
          resolve(true);
          return;
        }

        // Initialize PostHog with error handling
        posthog.init(config.apiKey, {
          api_host: config.apiHost,

          // Person profiles configuration
          person_profiles: 'identified_only',

          // Privacy settings - GDPR compliant
          respect_dnt: true,
          opt_out_capturing_by_default: true, // Start opted out, require explicit consent

          // IP anonymization
          property_blacklist: ['$ip'],

          // Cookie settings
          cross_subdomain_cookie: false,
          secure_cookie: isProduction(),

          // Development settings
          debug: isDevelopment(),

          // Disable features that require network requests in development
          disable_surveys: isDevelopment(),



          // Development mode: bypass some restrictions
          ...(isDevelopment() && {
            // Use different endpoint to bypass content blockers
            api_host: config.apiHost,
            // Disable some features that might be blocked
            autocapture: false,
            capture_pageview: false, // We'll handle this manually
            // Add detailed logging for development
            before_send: (event: any) => {
              if (event) {
                console.log('📤 PostHog sending event to dashboard:', {
                  event: event.event || 'unknown',
                  properties: event.properties || {},
                  distinct_id: event.distinct_id || 'unknown',
                  timestamp: new Date().toISOString()
                });
              }
              return event;
            },
          }),

          // Error handling
          on_request_error: (failedRequest: any) => {
            if (isDevelopment()) {
              console.warn('PostHog request error (continuing gracefully):', failedRequest);
            }
            // Don't throw errors, just log them
            return false; // Prevent PostHog from throwing
          },

          // Add request success logging in development
          ...(isDevelopment() && {
            before_send: (event: any) => {
              console.log('📤 PostHog sending event:', event);
              return event;
            }
          }),

          // Disable automatic error reporting
          capture_performance: false,

          // Disable console error capture to prevent interference
          capture_exceptions: {
            capture_unhandled_errors: !isDevelopment(), // Disable in dev to prevent script loading issues
            capture_unhandled_rejections: !isDevelopment(),
            capture_console_errors: false
          },

          // Loaded callback
          loaded: (posthogInstance) => {
            clearTimeout(timeoutId);

            if (isDevelopment()) {
              console.log('✅ PostHog loaded callback fired', {
                hasCapture: typeof posthogInstance?.capture === 'function',
                distinctId: posthogInstance?.get_distinct_id?.()
              });
            }

            // Verify PostHog is actually ready
            if (posthogInstance && typeof posthogInstance.capture === 'function') {
              resolve(true);
            } else {
              reject(new Error('PostHog instance is not properly initialized'));
            }
          },

          // GDPR compliance
          persistence: 'localStorage+cookie',
          persistence_name: 'ja_business_analytics',
          cookie_expiration: 365, // 1 year

          // Data retention
          session_recording: {
            maskAllInputs: true,
            recordCrossOriginIframes: false,
          },

          // Performance settings
          request_batching: isProduction(),

          // Capture settings
          capture_pageview: false, // We handle this manually
          capture_pageleave: true,

          // Disable features in development
          disable_session_recording: isDevelopment(),
          disable_compression: isDevelopment(),
        });

        // Fallback timeout in case loaded callback doesn't fire
        setTimeout(() => {
          if (posthog && typeof posthog.capture === 'function' && !this.context.endTime) {
            clearTimeout(timeoutId);
            if (isDevelopment()) {
              console.log('✅ PostHog initialized (fallback check)', {
                hasCapture: typeof posthog.capture === 'function',
                distinctId: posthog.get_distinct_id?.()
              });
            }
            resolve(true);
          }
        }, 2000);

      } catch (error) {
        clearTimeout(timeoutId);
        const errorObj = error as Error;
        
        // In development, treat network errors as non-fatal
        if (isDevelopment() && (
          errorObj.message.includes('Failed to fetch') ||
          errorObj.message.includes('Network') ||
          errorObj.message.includes('CORS')
        )) {
          console.warn('⚠️ PostHog network error (continuing in development mode):', errorObj.message);
          
          // Check if PostHog is still functional despite the error
          if (posthog && typeof posthog.capture === 'function') {
            console.log('✅ PostHog core functionality available despite network error');
            resolve(true);
            return;
          }
        }
        
        console.error('❌ PostHog initialization error:', errorObj);
        reject(errorObj);
      }
    });
  }

  /**
   * Get validated configuration
   */
  private getValidatedConfig(): { isValid: boolean; errors: string[]; config: any } {
    const errors: string[] = [];
    
    try {
      const envConfig = getEnvironmentConfig();
      const config = {
        apiKey: envConfig.posthog.apiKey,
        apiHost: envConfig.posthog.apiHost,
      };

      // Validate API key
      if (!config.apiKey || config.apiKey === 'placeholder-api-key') {
        errors.push('PostHog API key is missing or invalid');
      }

      // Validate API host
      if (!config.apiHost) {
        errors.push('PostHog API host is missing');
      } else {
        try {
          new URL(config.apiHost);
        } catch {
          errors.push('PostHog API host is not a valid URL');
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        config,
      };

    } catch (error) {
      errors.push(`Configuration error: ${(error as Error).message}`);
      return {
        isValid: false,
        errors,
        config: null,
      };
    }
  }

  /**
   * Check if PostHog is initialized
   */
  public isInitialized(): boolean {
    return this.context.state === InitializationState.INITIALIZED;
  }

  /**
   * Check if PostHog is currently initializing
   */
  public isInitializing(): boolean {
    return this.context.state === InitializationState.INITIALIZING;
  }

  /**
   * Get current health status
   */
  public getHealthStatus(): PostHogHealthStatus {
    return { ...this.healthStatus };
  }

  /**
   * Reset initialization state (for testing or recovery)
   */
  public reset(): void {
    console.log('Resetting PostHog initialization state');
    
    // Reset context
    this.context = {
      state: InitializationState.NOT_STARTED,
      retryCount: 0,
    };
    
    // Reset health status
    this.healthStatus = {
      initialized: false,
      healthy: false,
      initializationAttempts: 0,
      state: InitializationState.NOT_STARTED,
    };
    
    // Clear any pending initialization promise
    PostHogInitManager.initializationPromise = null;
    
    // Reset PostHog instance if it exists
    try {
      if (posthog && typeof posthog.reset === 'function') {
        posthog.reset();
      }
    } catch (error) {
      console.warn('Error resetting PostHog instance:', error);
    }
  }

  /**
   * Update initialization state
   */
  private updateState(newState: InitializationState): void {
    const oldState = this.context.state;
    this.context.state = newState;
    this.healthStatus.state = newState;
    
    if (isDevelopment() && oldState !== newState) {
      console.log(`PostHog state: ${oldState} → ${newState}`);
    }
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get PostHog instance (only if initialized)
   */
  public getPostHogInstance() {
    if (this.isInitialized()) {
      return posthog;
    }
    return null;
  }

  /**
   * Check if PostHog is ready for use
   */
  public isReady(): boolean {
    return this.isInitialized() && 
           this.healthStatus.healthy && 
           posthog && 
           typeof posthog.capture === 'function';
  }

  /**
   * Disable PostHog (for consent withdrawal)
   */
  public disable(): void {
    console.log('Disabling PostHog');
    this.updateState(InitializationState.DISABLED);
    this.healthStatus.initialized = false;
    this.healthStatus.healthy = false;
    
    try {
      if (posthog && typeof posthog.opt_out_capturing === 'function') {
        posthog.opt_out_capturing();
      }
    } catch (error) {
      console.warn('Error disabling PostHog:', error);
    }
  }

  /**
   * Enable PostHog (for consent granting)
   */
  public async enable(): Promise<boolean> {
    if (this.context.state === InitializationState.DISABLED) {
      console.log('Enabling PostHog');
      this.reset();
      return this.initialize();
    }
    return this.isReady();
  }
}

// Export singleton instance
export const postHogInitManager = PostHogInitManager.getInstance();

// Export convenience functions
export const initializePostHog = () => postHogInitManager.initialize();
export const isPostHogInitialized = () => postHogInitManager.isInitialized();
export const isPostHogInitializing = () => postHogInitManager.isInitializing();
export const isPostHogReady = () => postHogInitManager.isReady();
export const getPostHogHealthStatus = () => postHogInitManager.getHealthStatus();
export const resetPostHog = () => postHogInitManager.reset();
export const getPostHogInstance = () => postHogInitManager.getPostHogInstance();