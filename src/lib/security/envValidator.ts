/**
 * Runtime environment variable validator
 * Provides comprehensive validation and security checks for environment variables
 */

import { logSecurityEvent } from '../middleware/securityMiddleware';

/**
 * Environment variable validation rules
 */
interface ValidationRule {
  required: boolean;
  pattern?: RegExp;
  minLength?: number;
  maxLength?: number;
  allowedValues?: string[];
  serverOnly?: boolean;
  description: string;
}

/**
 * Comprehensive environment variable validation rules
 */
const ENV_VALIDATION_RULES: Record<string, ValidationRule> = {
  // Node.js Environment
  NODE_ENV: {
    required: true,
    allowedValues: ['development', 'production', 'test'],
    description: 'Node.js environment mode',
  },
  
  // Supabase Configuration (Public)
  NEXT_PUBLIC_SUPABASE_URL: {
    required: true,
    pattern: /^https:\/\/[a-z0-9]+\.supabase\.co$/,
    description: 'Supabase project URL',
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: true,
    pattern: /^eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$/,
    minLength: 100,
    description: 'Supabase anonymous key (JWT)',
  },
  
  // Supabase Configuration (Server-only)
  SUPABASE_SERVICE_ROLE_KEY: {
    required: false,
    pattern: /^eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$/,
    serverOnly: true,
    description: 'Supabase service role key (server-only)',
  },
  
  // Sentry Configuration (Public)
  NEXT_PUBLIC_SENTRY_DSN: {
    required: true,
    pattern: /^https:\/\/[a-f0-9]+@[a-z0-9-]+\.ingest\.(us\.)?sentry\.io\/[0-9]+$/,
    description: 'Sentry Data Source Name',
  },
  NEXT_PUBLIC_SENTRY_ENVIRONMENT: {
    required: true,
    allowedValues: ['development', 'staging', 'production'],
    description: 'Sentry environment identifier',
  },
  
  // Sentry Configuration (Server-only)
  SENTRY_AUTH_TOKEN: {
    required: false,
    pattern: /^sntrys_[a-f0-9]{64}$/,
    serverOnly: true,
    description: 'Sentry authentication token (server-only)',
  },
  SENTRY_ORG: {
    required: false,
    minLength: 1,
    maxLength: 50,
    serverOnly: true,
    description: 'Sentry organization slug',
  },
  SENTRY_PROJECT: {
    required: false,
    minLength: 1,
    maxLength: 50,
    serverOnly: true,
    description: 'Sentry project slug',
  },
  
  // PostHog Configuration (Public)
  NEXT_PUBLIC_POSTHOG_API_KEY: {
    required: true,
    pattern: /^phc_[a-zA-Z0-9]+$/,
    minLength: 20,
    description: 'PostHog API key',
  },
  NEXT_PUBLIC_POSTHOG_API_HOST: {
    required: true,
    pattern: /^https:\/\/.+/,
    description: 'PostHog API host URL',
  },
  
  // Security Configuration (Server-only)
  CORS_ORIGIN: {
    required: false,
    pattern: /^https:\/\/.+/,
    serverOnly: true,
    description: 'CORS allowed origin',
  },
  TRUSTED_HOSTS: {
    required: false,
    serverOnly: true,
    description: 'Comma-separated list of trusted hosts',
  },
  CSP_REPORT_URI: {
    required: false,
    pattern: /^https:\/\/.+/,
    serverOnly: true,
    description: 'Content Security Policy report URI',
  },
  
  // Database Configuration (Server-only)
  DATABASE_URL: {
    required: false,
    pattern: /^postgres:\/\/.+/,
    serverOnly: true,
    description: 'Database connection URL',
  },
  DB_CONNECTION_TIMEOUT: {
    required: false,
    pattern: /^\d+$/,
    serverOnly: true,
    description: 'Database connection timeout in milliseconds',
  },
  DB_MAX_CONNECTIONS: {
    required: false,
    pattern: /^\d+$/,
    serverOnly: true,
    description: 'Maximum database connections',
  },
};

/**
 * Validation result interface
 */
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  securityIssues: string[];
}

/**
 * Validates a single environment variable
 */
function validateSingleVar(name: string, value: string | undefined, rule: ValidationRule): {
  errors: string[];
  warnings: string[];
  securityIssues: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const securityIssues: string[] = [];
  
  // Check if required variable is missing
  if (rule.required && (!value || value.trim() === '')) {
    errors.push(`Missing required environment variable: ${name}`);
    return { errors, warnings, securityIssues };
  }
  
  // Skip validation if variable is not set and not required
  if (!value || value.trim() === '') {
    return { errors, warnings, securityIssues };
  }
  
  const trimmedValue = value.trim();
  
  // Check server-only variables on client side
  if (rule.serverOnly && typeof window !== 'undefined') {
    securityIssues.push(`Server-only variable ${name} is exposed to client`);
  }
  
  // Validate pattern
  if (rule.pattern && !rule.pattern.test(trimmedValue)) {
    errors.push(`Invalid format for ${name}: ${rule.description}`);
  }
  
  // Validate length
  if (rule.minLength && trimmedValue.length < rule.minLength) {
    errors.push(`${name} is too short (minimum ${rule.minLength} characters)`);
  }
  
  if (rule.maxLength && trimmedValue.length > rule.maxLength) {
    errors.push(`${name} is too long (maximum ${rule.maxLength} characters)`);
  }
  
  // Validate allowed values
  if (rule.allowedValues && !rule.allowedValues.includes(trimmedValue)) {
    errors.push(`Invalid value for ${name}. Allowed values: ${rule.allowedValues.join(', ')}`);
  }
  
  // Check for placeholder values in production
  if (process.env.NODE_ENV === 'production' && isPlaceholderValue(trimmedValue)) {
    securityIssues.push(`Production environment contains placeholder value for ${name}`);
  }
  
  // Check for development values in production
  if (process.env.NODE_ENV === 'production' && isDevelopmentValue(trimmedValue)) {
    warnings.push(`${name} appears to contain development/test values in production`);
  }
  
  return { errors, warnings, securityIssues };
}

/**
 * Checks if a value appears to be a placeholder
 */
function isPlaceholderValue(value: string): boolean {
  const placeholderPatterns = [
    /your_.*_here/i,
    /replace.*with.*actual/i,
    /todo.*replace/i,
    /example/i,
    /test.*key/i,
    /dummy/i,
    /placeholder/i,
    /changeme/i,
    /default/i,
  ];
  
  return placeholderPatterns.some(pattern => pattern.test(value));
}

/**
 * Checks if a value appears to be a development/test value
 */
function isDevelopmentValue(value: string): boolean {
  const devPatterns = [
    /localhost/i,
    /127\.0\.0\.1/i,
    /dev/i,
    /test/i,
    /staging/i,
    /demo/i,
  ];
  
  return devPatterns.some(pattern => pattern.test(value));
}

/**
 * Validates all environment variables
 */
export function validateAllEnvironmentVariables(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const securityIssues: string[] = [];
  
  // Validate each environment variable
  Object.entries(ENV_VALIDATION_RULES).forEach(([name, rule]) => {
    const value = process.env[name];
    const result = validateSingleVar(name, value, rule);
    
    errors.push(...result.errors);
    warnings.push(...result.warnings);
    securityIssues.push(...result.securityIssues);
  });
  
  // Additional cross-variable validations
  performCrossVariableValidation(errors, warnings, securityIssues);
  
  // Log security issues
  securityIssues.forEach(issue => {
    logSecurityEvent({
      type: 'validation_failure',
      message: issue,
      severity: 'high',
      details: { environment: process.env.NODE_ENV },
    });
  });
  
  return {
    isValid: errors.length === 0 && securityIssues.length === 0,
    errors,
    warnings,
    securityIssues,
  };
}

/**
 * Performs cross-variable validation checks
 */
function performCrossVariableValidation(
  errors: string[],
  warnings: string[],
  securityIssues: string[]
): void {
  // Check environment consistency
  const nodeEnv = process.env.NODE_ENV;
  const sentryEnv = process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT;
  
  if (nodeEnv === 'production' && sentryEnv !== 'production') {
    warnings.push('NODE_ENV is production but SENTRY_ENVIRONMENT is not');
  }
  
  // Check for mixed environment configurations
  if (nodeEnv === 'production') {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const posthogHost = process.env.NEXT_PUBLIC_POSTHOG_API_HOST;
    
    if (supabaseUrl?.includes('localhost') || posthogHost?.includes('localhost')) {
      securityIssues.push('Production environment contains localhost URLs');
    }
  }
  
  // Validate Sentry configuration completeness
  const sentryDsn = process.env.NEXT_PUBLIC_SENTRY_DSN;
  const sentryOrg = process.env.SENTRY_ORG;
  const sentryProject = process.env.SENTRY_PROJECT;
  
  if (sentryDsn && (!sentryOrg || !sentryProject)) {
    warnings.push('Sentry DSN is configured but organization or project is missing');
  }
}

/**
 * Validates environment variables for a specific context
 */
export function validateEnvironmentForContext(context: 'client' | 'server' | 'api'): ValidationResult {
  const contextRules = Object.entries(ENV_VALIDATION_RULES).filter(([_, rule]) => {
    switch (context) {
      case 'client':
        return !rule.serverOnly;
      case 'server':
        return rule.serverOnly;
      case 'api':
        return true; // API routes can access both client and server variables
      default:
        return true;
    }
  });
  
  const errors: string[] = [];
  const warnings: string[] = [];
  const securityIssues: string[] = [];
  
  contextRules.forEach(([name, rule]) => {
    const value = process.env[name];
    const result = validateSingleVar(name, value, rule);
    
    errors.push(...result.errors);
    warnings.push(...result.warnings);
    securityIssues.push(...result.securityIssues);
  });
  
  return {
    isValid: errors.length === 0 && securityIssues.length === 0,
    errors,
    warnings,
    securityIssues,
  };
}

/**
 * Gets environment variable documentation
 */
export function getEnvironmentDocumentation(): Record<string, {
  description: string;
  required: boolean;
  serverOnly: boolean;
  example?: string;
}> {
  const docs: Record<string, any> = {};
  
  Object.entries(ENV_VALIDATION_RULES).forEach(([name, rule]) => {
    docs[name] = {
      description: rule.description,
      required: rule.required,
      serverOnly: rule.serverOnly || false,
      example: getExampleValue(name, rule),
    };
  });
  
  return docs;
}

/**
 * Generates example values for documentation
 */
function getExampleValue(name: string, rule: ValidationRule): string | undefined {
  if (name.includes('URL')) {
    return 'https://example.com';
  }
  if (name.includes('KEY') || name.includes('TOKEN')) {
    return '[your-key-here]';
  }
  if (name.includes('ENVIRONMENT')) {
    return 'production';
  }
  if (rule.allowedValues) {
    return rule.allowedValues[0];
  }
  return undefined;
}

/**
 * Runtime environment check for critical variables
 */
export function performRuntimeEnvironmentCheck(): void {
  const result = validateAllEnvironmentVariables();
  
  if (!result.isValid) {
    const errorMessage = `Environment validation failed:\n${result.errors.join('\n')}`;
    
    if (result.securityIssues.length > 0) {
      const securityMessage = `Security issues detected:\n${result.securityIssues.join('\n')}`;
      console.error('🚨 SECURITY ISSUES:', securityMessage);
      
      // Exit on security issues in production
      if (process.env.NODE_ENV === 'production') {
        process.exit(1);
      }
    }
    
    if (result.errors.length > 0) {
      console.error('❌ Environment Errors:', errorMessage);
      
      // Exit on errors in production
      if (process.env.NODE_ENV === 'production') {
        process.exit(1);
      }
    }
  }
  
  if (result.warnings.length > 0) {
    console.warn('⚠️  Environment Warnings:', result.warnings.join('\n'));
  }
  
  if (result.isValid && result.warnings.length === 0) {
    console.log('✅ Environment validation passed');
  }
}