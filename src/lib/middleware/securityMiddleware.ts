/**
 * Security middleware for environment variable protection
 * Implements runtime security checks and protections
 */

import { NextRequest, NextResponse } from 'next/server';
import { getSecureServerConfig, sanitizeEnvForLogging } from '../env';

/**
 * Security headers to be applied to all responses
 */
const SECURITY_HEADERS = {
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Control referrer information
  'Referrer-Policy': 'origin-when-cross-origin',
  
  // Prevent XSS attacks
  'X-XSS-Protection': '1; mode=block',
  
  // Enforce HTTPS
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Control resource loading
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
} as const;

/**
 * Content Security Policy configuration
 */
function getCSPHeader(): string {
  const isDev = process.env.NODE_ENV === 'development';
  
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://app.posthog.com https://us.i.posthog.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://bdzvmmnbtwsfvrqepjuu.supabase.co https://app.posthog.com https://us.i.posthog.com https://o1219778.ingest.us.sentry.io",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ];
  
  // Add development-specific CSP rules
  if (isDev) {
    csp[1] = "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://app.posthog.com https://us.i.posthog.com";
    csp.push("connect-src 'self' ws://localhost:* http://localhost:* https://bdzvmmnbtwsfvrqepjuu.supabase.co https://app.posthog.com https://us.i.posthog.com https://o1219778.ingest.us.sentry.io");
  }
  
  // Add report URI if configured
  const secureConfig = getSecureServerConfig();
  if (secureConfig.security.cspReportUri) {
    csp.push(`report-uri ${secureConfig.security.cspReportUri}`);
  }
  
  return csp.join('; ');
}

/**
 * Validates request origin against trusted hosts
 */
function validateOrigin(request: NextRequest): boolean {
  const origin = request.headers.get('origin');
  const host = request.headers.get('host');
  
  if (!origin && !host) {
    return true; // Allow requests without origin (direct navigation)
  }
  
  try {
    const secureConfig = getSecureServerConfig();
    const trustedHosts = secureConfig.security.trustedHosts;
    
    if (trustedHosts.length === 0) {
      return true; // No restrictions configured
    }
    
    const requestHost = origin ? new URL(origin).hostname : host;
    return trustedHosts.some(trusted => 
      requestHost === trusted || requestHost?.endsWith(`.${trusted}`)
    );
  } catch (error) {
    console.error('Error validating origin:', error);
    return false;
  }
}

/**
 * Checks for sensitive data exposure in response
 */
function sanitizeResponse(response: NextResponse): NextResponse {
  // Remove any potentially sensitive headers
  const sensitiveHeaders = [
    'x-powered-by',
    'server',
    'x-aspnet-version',
    'x-aspnetmvc-version',
  ];
  
  sensitiveHeaders.forEach(header => {
    response.headers.delete(header);
  });
  
  return response;
}

/**
 * Main security middleware function
 */
export function securityMiddleware(request: NextRequest): NextResponse {
  const response = NextResponse.next();
  
  try {
    // Validate request origin
    if (!validateOrigin(request)) {
      console.warn(`🔒 SECURITY: Blocked request from untrusted origin: ${request.headers.get('origin')}`);
      return new NextResponse('Forbidden', { status: 403 });
    }
    
    // Apply security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    // Apply Content Security Policy
    response.headers.set('Content-Security-Policy', getCSPHeader());
    
    // Apply CORS headers if configured
    const secureConfig = getSecureServerConfig();
    if (secureConfig.security.corsOrigin) {
      response.headers.set('Access-Control-Allow-Origin', secureConfig.security.corsOrigin);
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    }
    
    // Sanitize response
    return sanitizeResponse(response);
    
  } catch (error) {
    console.error('🚨 Security middleware error:', error);
    
    // In production, fail securely
    if (process.env.NODE_ENV === 'production') {
      return new NextResponse('Internal Server Error', { status: 500 });
    }
    
    return response;
  }
}

/**
 * API route security wrapper
 */
export function withSecurity<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse> | NextResponse
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      // Perform security checks before handling request
      const request = args[0] as NextRequest;
      
      // Validate environment configuration
      if (process.env.NODE_ENV === 'production') {
        const sanitizedEnv = sanitizeEnvForLogging();
        console.log('🔒 API Security Check:', {
          method: request.method,
          url: request.url,
          timestamp: new Date().toISOString(),
          envVarsCount: Object.keys(sanitizedEnv).length,
        });
      }
      
      // Execute the handler
      const response = await handler(...args);
      
      // Apply security middleware to response
      return securityMiddleware(request, response);
      
    } catch (error) {
      console.error('🚨 API Security Error:', error);
      
      // Return secure error response
      return new NextResponse(
        JSON.stringify({ error: 'Internal Server Error' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}

/**
 * Environment variable exposure detection
 * Scans response content for accidentally exposed sensitive data
 */
export function detectSensitiveDataExposure(content: string): string[] {
  const exposedSecrets: string[] = [];
  
  // Patterns for common sensitive data
  const sensitivePatterns = [
    { name: 'Supabase Service Role Key', pattern: /eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/ },
    { name: 'Sentry Auth Token', pattern: /sntrys_[a-f0-9]{64}/ },
    { name: 'Database URL', pattern: /postgres:\/\/[^@]+:[^@]+@[^\/]+\/[^\s"']+/ },
    { name: 'API Key', pattern: /[a-zA-Z0-9_-]{32,}/ },
    { name: 'JWT Token', pattern: /eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+/ },
  ];
  
  sensitivePatterns.forEach(({ name, pattern }) => {
    if (pattern.test(content)) {
      exposedSecrets.push(name);
    }
  });
  
  return exposedSecrets;
}

/**
 * Logs security events for monitoring
 */
export function logSecurityEvent(event: {
  type: 'validation_failure' | 'origin_blocked' | 'sensitive_exposure' | 'config_error';
  message: string;
  details?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event: 'SECURITY_EVENT',
    type: event.type,
    message: event.message,
    severity: event.severity,
    details: event.details || {},
    environment: process.env.NODE_ENV,
  };
  
  // Log to console with appropriate level
  switch (event.severity) {
    case 'critical':
      console.error('🚨 CRITICAL SECURITY EVENT:', logEntry);
      break;
    case 'high':
      console.error('🔴 HIGH SECURITY EVENT:', logEntry);
      break;
    case 'medium':
      console.warn('🟡 MEDIUM SECURITY EVENT:', logEntry);
      break;
    case 'low':
      console.info('🔵 LOW SECURITY EVENT:', logEntry);
      break;
  }
  
  // In production, you might want to send to external monitoring
  if (process.env.NODE_ENV === 'production' && event.severity === 'critical') {
    // TODO: Send to external security monitoring service
  }
}