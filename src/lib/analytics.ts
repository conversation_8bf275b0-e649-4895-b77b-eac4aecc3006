/**
 * Simple, Modern PostHog Analytics Integration
 * 
 * This is a complete redesign that eliminates complexity while maintaining
 * all necessary functionality for production use.
 */

import posthog from 'posthog-js';
import { getEnvironmentConfig, isDevelopment } from './env';

// Simple event types for type safety
export interface AnalyticsEvents {
  page_view: { page: string; section: string; referrer?: string };
  contact_form_submit: { form_type: string; success: boolean };
  cta_click: { cta_type: string; location: string; text?: string };
  navigation_click: { section: string; method: 'scroll' | 'click' };
  service_inquiry: { service_type: string; contact_method: string };
}

// Simple state management
let isInitialized = false;
let initializationPromise: Promise<boolean> | null = null;

/**
 * Initialize PostHog with simple, robust configuration
 */
export async function initializeAnalytics(): Promise<boolean> {
  // Return existing promise if already initializing
  if (initializationPromise) {
    return initializationPromise;
  }

  // Return true if already initialized
  if (isInitialized) {
    return true;
  }

  // Create initialization promise
  initializationPromise = performInitialization();
  
  try {
    const result = await initializationPromise;
    return result;
  } finally {
    initializationPromise = null;
  }
}

/**
 * Perform the actual initialization
 */
async function performInitialization(): Promise<boolean> {
  try {
    const config = getEnvironmentConfig();
    
    // Validate configuration
    if (!config.posthog.apiKey || config.posthog.apiKey === 'placeholder-api-key') {
      if (isDevelopment()) {
        console.warn('⚠️ PostHog API key not configured - analytics disabled');
        return false;
      }
      throw new Error('PostHog API key is required');
    }

    // Initialize PostHog with simple, production-ready config
    posthog.init(config.posthog.apiKey, {
      api_host: config.posthog.apiHost,
      
      // Privacy-first configuration
      person_profiles: 'identified_only',
      respect_dnt: !isDevelopment(), // Ignore DNT in development
      opt_out_capturing_by_default: !isDevelopment(), // Auto opt-in for development
      
      // Security settings
      secure_cookie: !isDevelopment(),
      cross_subdomain_cookie: false,
      
      // Manual control over what we capture
      autocapture: false,
      capture_pageview: false,
      capture_pageleave: false,
      
      // Development settings
      debug: isDevelopment(),
      
      // Error handling - fail gracefully
      on_request_error: (error) => {
        if (isDevelopment()) {
          console.warn('PostHog request error (continuing gracefully):', error);
        }
        return false; // Don't throw errors
      },
      
      // Simple loaded callback
      loaded: (posthogInstance) => {
        isInitialized = true;
        if (isDevelopment()) {
          console.log('✅ PostHog initialized successfully', {
            distinctId: posthogInstance?.get_distinct_id?.(),
            hasOptedOut: posthogInstance?.has_opted_out_capturing?.(),
            apiHost: config.posthog.apiHost,
            apiKey: config.posthog.apiKey.substring(0, 10) + '...'
          });
          
          // Auto opt-in for development
          if (posthogInstance?.has_opted_out_capturing?.()) {
            posthogInstance.opt_in_capturing();
            console.log('🧪 Development mode: auto opted-in to capturing');
          }
        }
      }
    });

    // Wait for initialization with timeout
    await waitForInitialization(5000);
    
    return isInitialized;
    
  } catch (error) {
    console.error('PostHog initialization failed:', error);
    
    // In development, continue without analytics
    if (isDevelopment()) {
      console.warn('⚠️ Continuing without analytics in development mode');
      return false;
    }
    
    throw error;
  }
}

/**
 * Wait for PostHog to be ready
 */
function waitForInitialization(timeout: number): Promise<void> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const checkReady = () => {
      if (isInitialized || (posthog && posthog.__loaded)) {
        isInitialized = true;
        resolve();
        return;
      }
      
      if (Date.now() - startTime > timeout) {
        reject(new Error('PostHog initialization timeout'));
        return;
      }
      
      setTimeout(checkReady, 100);
    };
    
    checkReady();
  });
}

/**
 * Check if user has granted analytics consent
 */
function hasAnalyticsConsent(): boolean {
  if (typeof window === 'undefined') return false;
  
  // In development, allow tracking for testing
  if (isDevelopment()) {
    // Auto-grant consent in development for easier testing
    try {
      const consent = localStorage.getItem('ja_privacy_consent');
      if (!consent) {
        // Auto-grant consent in development
        localStorage.setItem('ja_privacy_consent', JSON.stringify({
          analytics: true,
          timestamp: new Date().toISOString()
        }));
        console.log('🧪 Development mode: auto-granted analytics consent');
        return true;
      }
      const parsed = JSON.parse(consent);
      return parsed.analytics === true;
    } catch {
      // Fallback: grant consent in development
      localStorage.setItem('ja_privacy_consent', JSON.stringify({
        analytics: true,
        timestamp: new Date().toISOString()
      }));
      return true;
    }
  }
  
  try {
    const consent = localStorage.getItem('ja_privacy_consent');
    if (!consent) return false;
    
    const parsed = JSON.parse(consent);
    return parsed.analytics === true;
  } catch {
    return false;
  }
}

/**
 * Track a typed event with automatic consent checking
 */
export function trackEvent<K extends keyof AnalyticsEvents>(
  eventName: K,
  properties: AnalyticsEvents[K]
): void {
  // Early returns for safety
  if (!isInitialized || !posthog) {
    if (isDevelopment()) {
      console.log('📊 Analytics not ready, event not tracked:', eventName);
    }
    return;
  }
  
  if (!hasAnalyticsConsent()) {
    if (isDevelopment()) {
      console.log('📊 No consent, event not tracked:', eventName);
    }
    return;
  }
  
  try {
    posthog.capture(eventName, {
      ...properties,
      timestamp: new Date().toISOString(),
      environment: isDevelopment() ? 'development' : 'production'
    });
    
    if (isDevelopment()) {
      console.log('📊 Event tracked:', eventName, properties);
    }
  } catch (error) {
    // Fail gracefully
    if (isDevelopment()) {
      console.warn('📊 Event tracking failed:', eventName, error);
    }
  }
}

/**
 * Convenience functions for common events
 */
export function trackPageView(page: string, section: string, referrer?: string): void {
  trackEvent('page_view', { page, section, referrer });
}

export function trackCTAClick(ctaType: string, location: string, text?: string): void {
  trackEvent('cta_click', { cta_type: ctaType, location, text });
}

export function trackContactFormSubmit(formType: string, success: boolean): void {
  trackEvent('contact_form_submit', { form_type: formType, success });
}

export function trackNavigation(section: string, method: 'scroll' | 'click'): void {
  trackEvent('navigation_click', { section, method });
}

export function trackServiceInquiry(serviceType: string, contactMethod: string): void {
  trackEvent('service_inquiry', { service_type: serviceType, contact_method: contactMethod });
}

/**
 * Identify a user (for authenticated users)
 */
export function identifyUser(userId: string, properties?: Record<string, any>): void {
  if (!isInitialized || !posthog || !hasAnalyticsConsent()) {
    return;
  }
  
  try {
    posthog.identify(userId, properties);
    
    if (isDevelopment()) {
      console.log('👤 User identified:', userId, properties);
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn('👤 User identification failed:', error);
    }
  }
}

/**
 * Set user properties
 */
export function setUserProperties(properties: Record<string, any>): void {
  if (!isInitialized || !posthog || !hasAnalyticsConsent()) {
    return;
  }
  
  try {
    posthog.people?.set(properties);
    
    if (isDevelopment()) {
      console.log('👤 User properties set:', properties);
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn('👤 Setting user properties failed:', error);
    }
  }
}

/**
 * Reset user session (for logout)
 */
export function resetUser(): void {
  if (!isInitialized || !posthog) {
    return;
  }
  
  try {
    posthog.reset();
    
    if (isDevelopment()) {
      console.log('🔄 User session reset');
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn('🔄 User session reset failed:', error);
    }
  }
}

/**
 * Handle consent changes
 */
export function handleConsentChange(hasConsent: boolean): void {
  if (!isInitialized || !posthog) {
    return;
  }
  
  try {
    if (hasConsent) {
      posthog.opt_in_capturing();
      if (isDevelopment()) {
        console.log('✅ Analytics consent granted');
      }
    } else {
      posthog.opt_out_capturing();
      if (isDevelopment()) {
        console.log('🚫 Analytics consent withdrawn');
      }
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn('⚠️ Consent change failed:', error);
    }
  }
}

/**
 * Check if analytics is ready
 */
export function isAnalyticsReady(): boolean {
  return isInitialized && !!posthog && hasAnalyticsConsent();
}

/**
 * Get PostHog instance (for advanced usage)
 */
export function getPostHogInstance() {
  return isInitialized ? posthog : null;
}

/**
 * Development-only debug function
 */
export function debugAnalytics(): void {
  if (!isDevelopment()) return;
  
  console.log('🔍 Analytics Debug Info:');
  console.log('- Initialized:', isInitialized);
  console.log('- PostHog exists:', !!posthog);
  console.log('- PostHog loaded:', posthog?.__loaded);
  console.log('- Has consent:', hasAnalyticsConsent());
  console.log('- Distinct ID:', posthog?.get_distinct_id?.());
  console.log('- Session ID:', posthog?.get_session_id?.());
  console.log('- Has opted out:', posthog?.has_opted_out_capturing?.());
  console.log('- API Host:', posthog?.config?.api_host);
  console.log('- API Key:', posthog?.config?.token?.substring(0, 10) + '...');
}

/**
 * Send a test event (development only)
 */
export function sendTestEvent(): void {
  if (!isDevelopment()) return;
  
  console.log('🧪 Sending test event...');
  trackEvent('cta_click', {
    cta_type: 'test_event',
    location: 'debug_console',
    text: 'Manual Test Event'
  });
}

// Export PostHog instance for backward compatibility
export { posthog };