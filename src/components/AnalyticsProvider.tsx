/**
 * Simple Analytics Provider
 * Handles initialization and provides debug component in development
 */

'use client';

import React, { useEffect } from 'react';
import { initializeAnalytics } from '../lib/analytics';
import { AnalyticsDebug } from './AnalyticsDebug';
import { isDevelopment } from '../lib/env';

// Import test utilities in development (conditional import)
if (typeof window !== 'undefined' && isDevelopment()) {
  import('../lib/test-analytics').catch(() => {
    // Ignore import errors in production builds
  });
}

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  useEffect(() => {
    // Initialize analytics on mount
    initializeAnalytics().catch(error => {
      if (isDevelopment()) {
        console.warn('Analytics initialization failed:', error);
      }
    });
  }, []);

  return (
    <>
      {children}
      {isDevelopment() && <AnalyticsDebug />}
    </>
  );
}