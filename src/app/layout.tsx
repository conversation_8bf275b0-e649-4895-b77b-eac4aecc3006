import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { AnalyticsProvider } from '../components/AnalyticsProvider'
import ErrorBoundary from '../components/ErrorBoundary'
import { SessionTrackingProvider } from '../components/SessionTrackingProvider'
import MonitoringProvider from '../components/MonitoringProvider'
import { SentryMonitor } from '../components/dev/SentryMonitor'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'J&A Business Solutions LLC',
  description: 'Premium property services and rental management solutions',
  manifest: '/manifest.json',
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#1e3a8a',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ErrorBoundary>
          <MonitoringProvider>
            <SessionTrackingProvider>
              <AnalyticsProvider>
                {children}
                <SentryMonitor />
              </AnalyticsProvider>
            </SessionTrackingProvider>
          </MonitoringProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}