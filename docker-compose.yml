version: '3.8'

services:
  web:
    image: atemndobs/jna-amd64:v0.9 
    container_name: jna-business-solutions
    restart: unless-stopped
    ports:
      - "8642:3000"
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    volumes:
      - ./public:/app/public
      - ./.env:/app/.env
    networks:
      - jna-network

networks:
  jna-network:
    driver: bridge
