version: '3.8'

services:
  web:
    image: atemndobs/jna-amd64:v0.12
    container_name: jna-business-solutions
    restart: unless-stopped
    ports:
      - "8642:3000"
    env_file:
      - .env.production
    environment:
      - NODE_ENV=production
      # Explicitly set critical environment variables as backup
      - NEXT_PUBLIC_SUPABASE_URL=https://bdzvmmnbtwsfvrqepjuu.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.j5Fanbr-kiqPdsCmJf736qP2ZT5LZQwo0p3-fmCyUbc
      - NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509747857260544
      - NEXT_PUBLIC_POSTHOG_API_KEY=phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1
      - NEXT_PUBLIC_POSTHOG_API_HOST=https://us.i.posthog.com
      - NEXT_PUBLIC_SENTRY_ENVIRONMENT=production
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    volumes:
      - ./public:/app/public
    networks:
      - jna-network

networks:
  jna-network:
    driver: bridge
