version: '3.8'

services:
  web:
    image: atemndobs/jna-amd64:v0.10
    container_name: jna-business-solutions-prod
    restart: unless-stopped
    
    # Port mapping - expose application on port 8642
    ports:
      - "8642:3000"
    
    # Environment configuration
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    
    # Environment file
    env_file:
      - .env.production
    
    # Health check configuration
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Volume mounts for persistent data and configuration
    volumes:
      # Mount public directory for static assets
      - ./public:/app/public:ro
      # Mount logs directory for application logs
      - ./logs:/app/logs
      # Mount environment file
      - ./.env.production:/app/.env.production:ro
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=jna-business-solutions"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # User configuration (run as non-root)
    user: "1000:1000"
    
    # Network configuration
    networks:
      - jna-network
    
    # Labels for monitoring and management
    labels:
      - "com.jna.service=web"
      - "com.jna.environment=production"
      - "com.jna.version=v0.10"
      - "traefik.enable=false"

  # Optional: Add a monitoring sidecar container
  monitoring:
    image: prom/node-exporter:latest
    container_name: jna-monitoring
    restart: unless-stopped
    
    # Expose metrics on port 9100
    ports:
      - "9100:9100"
    
    # Mount host filesystem for system metrics
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    
    # Command line arguments
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"
    
    # Network configuration
    networks:
      - jna-network
    
    # Labels
    labels:
      - "com.jna.service=monitoring"
      - "com.jna.environment=production"

# Network configuration
networks:
  jna-network:
    driver: bridge

# Volume configuration for persistent data
volumes:
  jna-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  
  jna-public:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./public